<!--公告发布/编辑页-->
<view class="container page-bottom-safe-area">
  
  <!-- 表单内容 -->
  <view class="form-container">
    
    <!-- 公告标题 -->
    <view class="form-item">
      <view class="form-label">公告标题 <text class="required">*</text></view>
      <input 
        class="form-input" 
        placeholder="请输入公告标题" 
        value="{{formData.title}}"
        bindinput="onTitleInput"
        maxlength="50"
      />
    </view>

    <!-- 通知类型 -->
    <view class="form-item">
      <view class="form-label">通知类型 <text class="required">*</text></view>
      <picker 
        mode="selector" 
        range="{{noticeTypeDict}}" 
        range-key="nameCn"
        value="{{typeIndex}}"
        bindchange="onTypeChange"
      >
        <view class="form-picker">
          <text class="picker-text">{{noticeTypeDict[typeIndex].nameCn || '请选择通知类型'}}</text>
          <text class="picker-arrow">></text>
        </view>
      </picker>
    </view>

    <!-- 通知对象 -->
    <view class="form-item">
      <view class="form-label">通知对象 <text class="required">*</text></view>
      <picker 
        mode="selector" 
        range="{{noticeTargetDict}}" 
        range-key="nameCn"
        value="{{targetIndex}}"
        bindchange="onTargetChange"
      >
        <view class="form-picker">
          <text class="picker-text">{{noticeTargetDict[targetIndex].nameCn || '请选择通知对象'}}</text>
          <text class="picker-arrow">></text>
        </view>
      </picker>
    </view>

    <!-- 楼栋选择（当选择楼栋时显示） -->
    <view class="form-item" wx:if="{{formData.targetType === 'building'}}">
      <view class="form-label">选择楼栋 <text class="required">*</text></view>
      <view class="form-picker" bindtap="showBuildingSelector">
        <view class="picker-text">
          {{selectedBuildings.length > 0 ? selectedBuildings.length + '个楼栋' : '请选择楼栋'}}
        </view>
        <text class="picker-arrow">></text>
      </view>

      <!-- 已选楼栋列表 -->
      <view class="selected-buildings" wx:if="{{selectedBuildings.length > 0}}">
        <view class="building-tag" wx:for="{{selectedBuildings}}" wx:key="id">
          {{item.buildingNumber}}
        </view>
      </view>
    </view>

    <!-- 公告内容 -->
    <view class="form-item">
      <view class="form-label">公告内容 <text class="required">*</text></view>
      <textarea 
        class="form-textarea" 
        placeholder="请输入公告内容" 
        value="{{formData.content}}"
        bindinput="onContentInput"
        maxlength="1000"
        show-confirm-bar="{{false}}"
      />
    </view>

    <!-- 图片上传 -->
    <view class="form-item">
      <view class="form-label">图片</view>
      <view class="image-upload">
        <view class="image-preview" wx:if="{{formData.imageUrl}}">
          <image src="{{formData.imageUrl.startsWith('http') ? formData.imageUrl : (formData.imageUrl.startsWith('/') ? apiUrl + '/common-api/v1/file' + formData.imageUrl : formData.imageUrl)}}" mode="aspectFill" />
          <view class="image-delete" bindtap="deleteImage">
            <text class="delete-icon">×</text>
          </view>
        </view>
        <view class="upload-btn" wx:else bindtap="chooseImage">
          <text class="upload-icon">+</text>
          <text class="upload-text">添加图片</text>
        </view>
      </view>
    </view>

    <!-- 置顶设置 -->
    <view class="form-item">
      <view class="form-label">置顶</view>
      <switch 
        checked="{{formData.top}}" 
        bindchange="onTopChange"
        color="#ff8c00"
      />
    </view>

    <!-- 定时发布（暂时隐藏） -->
    <!-- <view class="form-item" style="display: none;">
      <view class="form-label">定时发布</view>
      <switch checked="{{false}}" disabled />
    </view> -->

  </view>

  <!-- 底部操作栏 -->
  <view class="bottom-actions">
    <button class="action-btn draft-btn" bindtap="saveDraft">保存草稿</button>
    <button class="action-btn draft-btn" bindtap="showDraftBox">草稿箱</button>
    <button 
      class="action-btn publish-btn" 
      bindtap="publishAnnouncement"
      disabled="{{isSubmitting}}"
    >
      {{isSubmitting ? '发布中...' : '立即发布'}}
    </button>
  </view>

</view>

<!-- 草稿箱弹窗 -->
<view class="modal-overlay" wx:if="{{showDraftModal}}" bindtap="closeDraftModal">
  <view class="draft-modal" catchtap="">
    <view class="modal-header">
      <text class="modal-title">草稿箱</text>
      <text class="modal-close" bindtap="closeDraftModal">×</text>
    </view>
    
    <view class="draft-list">
      <view class="draft-item" wx:for="{{draftList}}" wx:key="id">
        <view class="draft-content" bindtap="selectDraft" data-draft="{{item}}">
          <view class="draft-title">{{item.title || '无标题'}}</view>
          <view class="draft-time">{{item.updateTime || item.createTime}}</view>
        </view>
        <view class="draft-delete" bindtap="deleteDraft" data-id="{{item.id}}">
          删除
        </view>
      </view>
      
      <view class="empty-draft" wx:if="{{draftList.length === 0}}">
        <text>暂无草稿</text>
      </view>
    </view>
  </view>
</view>

<!-- 楼栋选择弹窗 -->
<view class="modal-overlay" wx:if="{{showBuildingModal}}" catchtap="closeBuildingModal">
  <view class="building-modal" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">选择楼栋</text>
      <text class="modal-close" catchtap="closeBuildingModal">×</text>
    </view>

    <view class="building-list">
      <view class="building-item" wx:for="{{buildingList}}" wx:key="id">
        <label class="building-label" bindtap="toggleBuildingSelection" data-building="{{item}}">
          <checkbox
            value="{{item.id}}"
            checked="{{item.selected}}"
          />
          <text class="building-name">{{item.buildingNumber}}</text>
        </label>
      </view>

      <view class="empty-building" wx:if="{{buildingList.length === 0}}">
        <text>暂无楼栋数据</text>
      </view>
    </view>

    <view class="modal-footer">
      <button class="modal-btn cancel-btn" catchtap="closeBuildingModal">取消</button>
      <button class="modal-btn confirm-btn" catchtap="confirmBuildingSelection">确定</button>
    </view>
  </view>
</view>
