// pages/property/resident/statistics/index.js
const util = require('../../../../../utils/util.js')
const propertyApi = require('../../../../../api/propertyApi.js')
const wxCharts = require('../../../../../utils/wxcharts/wxcharts.js');


Page({
  data: {
    // 统计数据
    statistics: {
      genderCount: {},
      buildingResidentCount: {},
      ageCount: {},
      typeCount: {},
      trendCount: [],
      roomTypeCount: {}
    },

    // 当前激活的标签
    activeTab: 'overview',

    // 是否正在加载
    isLoading: false,

    // 字典数据
    residentTypeDict: [],
    genderDict: [],
    roomTypeDict: [],

    // 图表实例
    residentTypeChart: null,
    ageChart: null,
    trendChart: null,
    roomTypeChart: null,
    buildingChart: null
  },

  onLoad: function() {
    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '居民统计'
    });

    // 初始化字典数据
    this.initDictData();

    // 加载数据
    this.loadData();
  },

  // 初始化字典数据
  initDictData: function() {
    try {
      // 获取居民类型字典
      const residentTypeDict = util.getDictByNameEn('resident_type');
      const residentTypeOptions = residentTypeDict && residentTypeDict.length > 0 && residentTypeDict[0].children ? residentTypeDict[0].children : [];

      // 获取性别字典
      const genderDict = util.getDictByNameEn('gender');
      const genderOptions = genderDict && genderDict.length > 0 && genderDict[0].children ? genderDict[0].children : [];

      // 获取房屋类型字典
      const roomTypeDict = util.getDictByNameEn('room_type');
      const roomTypeOptions = roomTypeDict && roomTypeDict.length > 0 && roomTypeDict[0].children ? roomTypeDict[0].children : [];

      this.setData({
        residentTypeDict: residentTypeOptions,
        genderDict: genderOptions,
        roomTypeDict: roomTypeOptions
      });

      console.log('字典数据初始化完成:', {
        residentTypeDict: residentTypeOptions,
        genderDict: genderOptions,
        roomTypeDict: roomTypeOptions
      });
    } catch (error) {
      console.error('初始化字典数据失败:', error);
    }
  },

  // 加载数据
  loadData: function() {
    this.setData({
      isLoading: true
    });

    const selectedCommunity = wx.getStorageSync('selectedCommunity');
    if (!selectedCommunity || !selectedCommunity.id) {
      console.error('未选择小区');
      wx.showToast({
        title: '请先选择小区',
        icon: 'none'
      });
      this.setData({ isLoading: false });
      return;
    }

    const params = {
      communityId: selectedCommunity.id
    };

    console.log('加载居民统计数据，参数:', params);

    propertyApi.getResidentStatistics(params)
      .then(res => {
        console.log('居民统计API响应:', res);
        if (res) {
          this.processStatisticsData(res);
        } else {
          console.log('居民统计数据为空');
          this.setData({ isLoading: false });
        }
      })
      .catch(error => {
        console.error('获取居民统计数据失败:', error);
        wx.showToast({
          title: '获取数据失败',
          icon: 'none'
        });
        this.setData({ isLoading: false });
      });
  },

  // 处理统计数据
  processStatisticsData: function(data) {
    console.log('处理统计数据:', data);

    // 处理居民类型分布数据
    const typeData = this.processTypeData(data.typeCount || {});

    // 处理性别分布数据
    const genderData = this.processGenderData(data.genderCount || {});

    // 处理年龄分布数据
    const ageData = this.processAgeData(data.ageCount || {});

    // 处理房屋类型分布数据
    const roomTypeData = this.processRoomTypeData(data.roomTypeCount || {});

    // 处理楼栋分布数据
    const buildingData = this.processBuildingData(data.buildingResidentCount || {});

    // 处理趋势数据
    const trendData = data.trendCount || [];

    // 计算年度总数
    const yearlyTotal = trendData.reduce((sum, count) => sum + count, 0);

    // 计算性别总数
    const genderTotal = Object.values(data.genderCount || {}).reduce((sum, count) => sum + count, 0);

    this.setData({
      'statistics.typeCount': data.typeCount,
      'statistics.genderCount': data.genderCount,
      'statistics.ageCount': data.ageCount,
      'statistics.roomTypeCount': data.roomTypeCount,
      'statistics.buildingResidentCount': data.buildingResidentCount,
      'statistics.trendCount': trendData,
      'statistics.yearlyTotal': yearlyTotal,
      'statistics.genderTotal': genderTotal,
      'statistics.typeData': typeData,
      'statistics.genderData': genderData,
      'statistics.ageData': ageData,
      'statistics.roomTypeData': roomTypeData,
      'statistics.buildingData': buildingData,
      isLoading: false
    });

    console.log('统计数据处理完成');

    // 延迟渲染图表，确保DOM已更新
    setTimeout(() => {
      this.renderCharts();
    }, 100);
  },

  // 处理居民类型分布数据
  processTypeData: function(typeCount) {
    const typeData = [];
    const residentTypeDict = this.data.residentTypeDict;

    Object.keys(typeCount).forEach(key => {
      const count = typeCount[key];
      const dictItem = residentTypeDict.find(item => item.nameEn === key);
      const name = dictItem ? dictItem.nameCn : key;

      typeData.push({
        name: name,
        value: count,
        nameEn: key
      });
    });

    return typeData;
  },

  // 处理性别分布数据
  processGenderData: function(genderCount) {
    const genderData = [];
    const genderDict = this.data.genderDict;

    // 计算总数
    const total = Object.values(genderCount).reduce((sum, count) => sum + count, 0);

    Object.keys(genderCount).forEach(key => {
      const count = genderCount[key];
      const dictItem = genderDict.find(item => item.nameEn === key);
      const name = dictItem ? dictItem.nameCn : key;
      const percent = total > 0 ? Math.round(count * 100 / total) : 0;

      genderData.push({
        name: name,
        value: count,
        percent: percent,
        nameEn: key
      });
    });

    return genderData;
  },

  // 处理年龄分布数据
  processAgeData: function(ageCount) {
    const ageData = [];
    const ageMapping = {
      'under20': '20岁以下',
      'age20to30': '20-30岁',
      'age30to40': '30-40岁',
      'age40to50': '40-50岁',
      'over50': '50岁以上'
    };

    // 计算总数
    const total = Object.values(ageCount).reduce((sum, count) => sum + count, 0);

    Object.keys(ageCount).forEach(key => {
      const count = ageCount[key];
      const name = ageMapping[key] || key;
      const percent = total > 0 ? Math.round(count * 100 / total) : 0;

      ageData.push({
        name: name,
        value: count,
        percent: percent,
        nameEn: key
      });
    });

    return ageData;
  },

  // 处理房屋类型分布数据
  processRoomTypeData: function(roomTypeCount) {
    const roomTypeData = [];
    const roomTypeDict = this.data.roomTypeDict;

    Object.keys(roomTypeCount).forEach(key => {
      const count = roomTypeCount[key];
      const dictItem = roomTypeDict.find(item => item.nameEn === key);
      const name = dictItem ? dictItem.nameCn : key;

      roomTypeData.push({
        name: name,
        value: count,
        nameEn: key
      });
    });

    return roomTypeData;
  },

  // 处理楼栋分布数据
  processBuildingData: function(buildingResidentCount) {
    const buildingData = [];

    Object.keys(buildingResidentCount).forEach(key => {
      const count = buildingResidentCount[key];

      buildingData.push({
        name: key,
        value: count
      });
    });

    return buildingData;
  },

  // 渲染图表
  renderCharts: function() {
    const activeTab = this.data.activeTab;

    // 根据当前激活的标签渲染相应的图表
    switch(activeTab) {
      case 'overview':
        this.renderResidentTypeChart();
        break;
      case 'trend':
        this.renderTrendChart();
        break;
      case 'age':
        this.renderAgeChart();
        break;
      case 'house':
        this.renderRoomTypeChart();
        this.renderBuildingChart();
        break;
    }
  },

  // 渲染居民类型分布饼图
  renderResidentTypeChart: function() {
    const typeData = this.data.statistics.typeData || [];
    if (typeData.length === 0) return;

    const chartData = typeData.map(item => ({
      name: item.name,
      data: item.value
    }));

    // 创建饼图
    const ctx = wx.createCanvasContext('residentTypeCanvas');

    wxCharts.create({
      type: 'pie',
      canvas: ctx,
      series: chartData,
      width: 320,
      height: 200,
      padding: 10,
      colors: ['#7cb5ec', '#f7a35c', '#434348'],
      showLegend: false
    });

     // 绘制到画布
     ctx.draw();
    // this.data.residentTypeChart = wxcharts.create({
    //   canvasId: 'residentTypeCanvas',
    //   type: 'pie',
    //   series: chartData,
    //   width: 320,
    //   height: 200,
    //   dataLabel: true,
    //   legend: false
    // });
  },

  // 渲染趋势分析柱状图
  renderTrendChart: function() {
    const trendData = this.data.statistics.trendCount || [];
    if (trendData.length === 0) return;

    const categories = ['1月','2月','3月','4月','5月','6月','7月','8月','9月','10月','11月','12月'];
    const chartData = [
      { name: '新增居民', data: trendData }
    ];

    // 创建柱状图
    const ctx = wx.createCanvasContext('trendCanvas');
    
    wxCharts.create({
      type: 'column',
      canvas: ctx,
      series: chartData,
      categories: categories,
      width: 320,
      height: 200,
      padding: 10,
      colors: ['#FF8C00'],
      showLegend: false
    });

    // 绘制到画布
    ctx.draw();
  },

  // 渲染年龄分布柱状图
  renderAgeChart: function() {
    const ageData = this.data.statistics.ageData || [];
    if (ageData.length === 0) return;

    const categories = ageData.map(item => item.name);
    const chartData = [
      { name: '人数', data: ageData.map(item => item.value) }
    ];

    // 创建柱状图
    const ctx = wx.createCanvasContext('ageCanvas');

    wxCharts.create({
      type: 'column',
      canvas: ctx,
      series: chartData,
      categories: categories,
      width: 320,
      height: 200,
      padding: 10,
      colors: ['#007AFF'],
      showLegend: false
    });

    // 绘制到画布
    ctx.draw();
  },

  // 渲染房屋类型分布饼图
  renderRoomTypeChart: function() {
    const roomTypeData = this.data.statistics.roomTypeData || [];
    if (roomTypeData.length === 0) return;

    const chartData = roomTypeData.map(item => ({
      name: item.name,
      data: item.value
    }));

    // 创建饼图
    const ctx = wx.createCanvasContext('roomTypeCanvas');

    wxCharts.create({
      type: 'pie',
      canvas: ctx,
      series: chartData,
      width: 320,
      height: 200,
      padding: 10,
      colors: ['#FF8C00', '#007AFF', '#34C759', '#FF3B30', '#5856D6'],
      showLegend: false
    });

    // 绘制到画布
    ctx.draw();
  },

  // 渲染楼栋分布柱状图
  renderBuildingChart: function() {
    const buildingData = this.data.statistics.buildingData || [];
    if (buildingData.length === 0) return;

    const categories = buildingData.map(item => item.name);
    const chartData = [
      { name: '居民数', data: buildingData.map(item => item.value) }
    ];

    // 创建柱状图
    const ctx = wx.createCanvasContext('buildingCanvas');

    wxCharts.create({
      type: 'column',
      canvas: ctx,
      series: chartData,
      categories: categories,
      width: 320,
      height: 200,
      padding: 10,
      colors: ['#34C759'],
      showLegend: false
    });

    // 绘制到画布
    ctx.draw();
  },

  // 切换标签
  switchTab: function(e) {
    const tab = e.currentTarget.dataset.tab;

    if (this.data.activeTab !== tab) {
      this.setData({
        activeTab: tab
      });

      // 延迟渲染图表，确保DOM已更新
      setTimeout(() => {
        this.renderCharts();
      }, 100);
    }
  },

  // 下拉刷新
  onPullDownRefresh: function() {
    // 重新加载数据
    this.loadData();

    // 停止下拉刷新
    wx.stopPullDownRefresh();
  },

  // 导出统计数据
  exportStatistics: function() {
    wx.showToast({
      title: '导出功能开发中',
      icon: 'none'
    });
  }
})