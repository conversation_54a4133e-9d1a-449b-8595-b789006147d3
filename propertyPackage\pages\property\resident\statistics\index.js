// pages/property/resident/statistics/index.js
const util = require('../../../../../utils/util.js')

Page({
  data: {
    // 统计数据
    statistics: {
      totalResidents: 256,
      ownerCount: 156,
      tenantCount: 68,
      familyCount: 32,
      verifiedCount: 220,
      unverifiedCount: 36,
      maleCount: 142,
      femaleCount: 114,
      // 月度新增居民数据
      monthlyNewResidents: [15, 22, 18, 25, 30, 28, 35, 42, 38, 45, 52, 48],
      // 年龄分布数据
      ageDistribution: [
        { range: '18岁以下', count: 15 },
        { range: '18-30岁', count: 68 },
        { range: '31-45岁', count: 95 },
        { range: '46-60岁', count: 58 },
        { range: '60岁以上', count: 20 }
      ],
      // 房屋类型分布
      houseTypeDistribution: [
        { type: '一室一厅', count: 45 },
        { type: '两室一厅', count: 78 },
        { type: '两室两厅', count: 62 },
        { type: '三室两厅', count: 48 },
        { type: '四室两厅', count: 23 }
      ],
      // 楼栋分布
      buildingDistribution: [
        { building: '1栋', count: 52 },
        { building: '2栋', count: 48 },
        { building: '3栋', count: 56 },
        { building: '4栋', count: 50 },
        { building: '5栋', count: 50 }
      ]
    },

    // 当前激活的标签
    activeTab: 'overview',

    // 时间范围
    timeRange: 'month',

    // 是否正在加载
    isLoading: false,

    // 周数据
    weekData: {
      totalResidents: 245,
      ownerCount: 150,
      tenantCount: 65,
      familyCount: 30,
      verifiedCount: 210,
      unverifiedCount: 35,
      maleCount: 135,
      femaleCount: 110,
      monthlyNewResidents: [5, 3, 4, 6, 8, 7, 10]
    }
  },

  onLoad: function() {
    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '居民统计'
    });

    // 加载数据
    this.loadData();
  },

  // 加载数据
  loadData: function() {
    this.setData({
      isLoading: true
    });

    // 计算性别百分比
    const { maleCount, femaleCount } = this.data.statistics;
    const total = maleCount + femaleCount;
    const malePercent = Math.round(maleCount * 100 / total);
    const femalePercent = Math.round(femaleCount * 100 / total);

    // 计算年龄分布百分比
    const ageDistribution = this.data.statistics.ageDistribution.map(item => {
      const ageTotal = this.data.statistics.ageDistribution.reduce((sum, curr) => sum + curr.count, 0);
      return {
        ...item,
        percent: Math.round(item.count * 100 / ageTotal)
      };
    });

    // 计算年度新增总数
    const yearlyNewTotal = this.data.statistics.monthlyNewResidents.reduce((a, b) => a + b, 0);

    // 更新数据
    setTimeout(() => {
      this.setData({
        'statistics.malePercent': malePercent,
        'statistics.femalePercent': femalePercent,
        'statistics.ageDistribution': ageDistribution,
        'statistics.yearlyNewTotal': yearlyNewTotal,
        isLoading: false
      });
    }, 1000);
  },

  // 切换标签
  switchTab: function(e) {
    const tab = e.currentTarget.dataset.tab;

    if (this.data.activeTab !== tab) {
      this.setData({
        activeTab: tab,
        isLoading: true
      });

      // 模拟数据加载
      setTimeout(() => {
        this.setData({
          isLoading: false
        });
      }, 500);
    }
  },

  // 切换时间范围
  switchTimeRange: function(e) {
    const range = e.currentTarget.dataset.range;

    if (this.data.timeRange !== range) {
      this.setData({
        timeRange: range,
        isLoading: true
      });

      // 模拟数据加载
      setTimeout(() => {
        this.setData({
          isLoading: false
        });
      }, 500);
    }
  },

  // 渲染图表
  renderCharts: function() {
    const activeTab = this.data.activeTab;

    // 根据当前激活的标签渲染相应的图表
    switch(activeTab) {
      case 'overview':
        if (!this.data.chartsRendered.residentType) {
          this.renderResidentTypeChart();
        }
        break;
      case 'trend':
        if (!this.data.chartsRendered.monthlyNew) {
          this.renderMonthlyNewChart();
        }
        break;
      case 'age':
        if (!this.data.chartsRendered.ageDistribution) {
          this.renderAgeDistributionChart();
        }
        break;
      case 'house':
        if (!this.data.chartsRendered.houseType) {
          this.renderHouseTypeChart();
        }
        if (!this.data.chartsRendered.buildingDistribution) {
          this.renderBuildingDistributionChart();
        }
        break;
    }
  },

  // 渲染居民类型分布图表（饼图）
  renderResidentTypeChart: function() {
    const ctx = wx.createCanvasContext('residentTypeChart');
    const { ownerCount, tenantCount, familyCount } = this.data.statistics;
    const total = ownerCount + tenantCount + familyCount;

    // 定义颜色
    const colors = ['#FF8C00', '#007AFF', '#34C759'];

    // 绘制饼图
    let startAngle = 0;
    const data = [
      { name: '业主', value: ownerCount, color: colors[0] },
      { name: '租户', value: tenantCount, color: colors[1] },
      { name: '家属', value: familyCount, color: colors[2] }
    ];

    const centerX = this.data.chartConfig.residentType.width / 2;
    const centerY = this.data.chartConfig.residentType.height / 2;
    const radius = Math.min(centerX, centerY) - 50;

    data.forEach((item, index) => {
      const portion = item.value / total;
      const endAngle = startAngle + portion * 2 * Math.PI;

      // 绘制扇形
      ctx.beginPath();
      ctx.moveTo(centerX, centerY);
      ctx.arc(centerX, centerY, radius, startAngle, endAngle);
      ctx.setFillStyle(item.color);
      ctx.fill();

      // 计算标签位置
      const midAngle = startAngle + (endAngle - startAngle) / 2;
      const labelRadius = radius * 0.7;
      const labelX = centerX + labelRadius * Math.cos(midAngle);
      const labelY = centerY + labelRadius * Math.sin(midAngle);

      // 绘制标签
      ctx.setFontSize(14);
      ctx.setFillStyle('#FFFFFF');
      ctx.setTextAlign('center');
      ctx.setTextBaseline('middle');
      ctx.fillText(`${item.value}`, labelX, labelY);

      startAngle = endAngle;
    });

    // 绘制中心白色圆形
    ctx.beginPath();
    ctx.arc(centerX, centerY, radius * 0.5, 0, 2 * Math.PI);
    ctx.setFillStyle('#FFFFFF');
    ctx.fill();

    // 绘制图例
    const legendX = 30;
    let legendY = this.data.chartConfig.residentType.height - 80;

    data.forEach((item, index) => {
      // 绘制颜色方块
      ctx.beginPath();
      ctx.rect(legendX, legendY, 15, 15);
      ctx.setFillStyle(item.color);
      ctx.fill();

      // 绘制文字
      ctx.setFontSize(12);
      ctx.setFillStyle('#333333');
      ctx.setTextAlign('left');
      ctx.fillText(`${item.name}: ${item.value}人`, legendX + 25, legendY + 8);

      legendY += 25;
    });

    ctx.draw();

    // 更新图表渲染状态
    const chartsRendered = this.data.chartsRendered;
    chartsRendered.residentType = true;
    this.setData({
      chartsRendered: chartsRendered
    });
  },

  // 渲染月度新增居民图表（柱状图）
  renderMonthlyNewChart: function() {
    const ctx = wx.createCanvasContext('monthlyNewChart');
    const { monthlyNewResidents } = this.data.statistics;
    const months = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'];

    const width = this.data.chartConfig.monthlyNew.width;
    const height = this.data.chartConfig.monthlyNew.height;
    const padding = { top: 40, right: 20, bottom: 60, left: 40 };

    const chartWidth = width - padding.left - padding.right;
    const chartHeight = height - padding.top - padding.bottom;

    // 计算最大值
    const maxValue = Math.max(...monthlyNewResidents) * 1.2;

    // 绘制坐标轴
    ctx.beginPath();
    ctx.setLineWidth(1);
    ctx.setStrokeStyle('#CCCCCC');

    // X轴
    ctx.moveTo(padding.left, height - padding.bottom);
    ctx.lineTo(width - padding.right, height - padding.bottom);

    // Y轴
    ctx.moveTo(padding.left, padding.top);
    ctx.lineTo(padding.left, height - padding.bottom);
    ctx.stroke();

    // 绘制X轴标签
    ctx.setFontSize(12);
    ctx.setFillStyle('#666666');
    ctx.setTextAlign('center');

    const barWidth = chartWidth / months.length * 0.6;
    const barSpacing = chartWidth / months.length;

    months.forEach((month, index) => {
      const x = padding.left + barSpacing * (index + 0.5);
      ctx.fillText(month, x, height - padding.bottom + 20);

      // 绘制柱状
      const barHeight = (monthlyNewResidents[index] / maxValue) * chartHeight;
      const barX = x - barWidth / 2;
      const barY = height - padding.bottom - barHeight;

      ctx.beginPath();
      ctx.rect(barX, barY, barWidth, barHeight);
      ctx.setFillStyle('#FF8C00');
      ctx.fill();

      // 绘制数值
      ctx.setFillStyle('#333333');
      ctx.fillText(monthlyNewResidents[index].toString(), x, barY - 10);
    });

    // 绘制Y轴标签
    ctx.setTextAlign('right');
    const yStep = maxValue / 5;

    for (let i = 0; i <= 5; i++) {
      const y = height - padding.bottom - (i / 5) * chartHeight;
      const value = Math.round(i * yStep);

      ctx.fillText(value.toString(), padding.left - 10, y + 5);

      // 绘制网格线
      ctx.beginPath();
      ctx.setLineWidth(0.5);
      ctx.setLineDash([5, 5]);
      ctx.moveTo(padding.left, y);
      ctx.lineTo(width - padding.right, y);
      ctx.stroke();
      ctx.setLineDash([]);
    }

    // 绘制标题
    ctx.setFontSize(16);
    ctx.setFillStyle('#000000');
    ctx.setTextAlign('center');
    ctx.fillText('月度新增居民统计', width / 2, 20);

    ctx.draw();

    // 更新图表渲染状态
    const chartsRendered = this.data.chartsRendered;
    chartsRendered.monthlyNew = true;
    this.setData({
      chartsRendered: chartsRendered
    });
  },

  // 渲染年龄分布图表（柱状图）
  renderAgeDistributionChart: function() {
    const ctx = wx.createCanvasContext('ageDistributionChart');
    const { ageDistribution } = this.data.statistics;

    const width = this.data.chartConfig.ageDistribution.width;
    const height = this.data.chartConfig.ageDistribution.height;
    const padding = { top: 40, right: 20, bottom: 60, left: 40 };

    const chartWidth = width - padding.left - padding.right;
    const chartHeight = height - padding.top - padding.bottom;

    // 计算最大值
    const maxValue = Math.max(...ageDistribution.map(item => item.count)) * 1.2;

    // 绘制坐标轴
    ctx.beginPath();
    ctx.setLineWidth(1);
    ctx.setStrokeStyle('#CCCCCC');

    // X轴
    ctx.moveTo(padding.left, height - padding.bottom);
    ctx.lineTo(width - padding.right, height - padding.bottom);

    // Y轴
    ctx.moveTo(padding.left, padding.top);
    ctx.lineTo(padding.left, height - padding.bottom);
    ctx.stroke();

    // 绘制X轴标签
    ctx.setFontSize(12);
    ctx.setFillStyle('#666666');
    ctx.setTextAlign('center');

    const barWidth = chartWidth / ageDistribution.length * 0.6;
    const barSpacing = chartWidth / ageDistribution.length;

    ageDistribution.forEach((item, index) => {
      const x = padding.left + barSpacing * (index + 0.5);
      ctx.fillText(item.range, x, height - padding.bottom + 20);

      // 绘制柱状
      const barHeight = (item.count / maxValue) * chartHeight;
      const barX = x - barWidth / 2;
      const barY = height - padding.bottom - barHeight;

      ctx.beginPath();
      ctx.rect(barX, barY, barWidth, barHeight);
      ctx.setFillStyle('#007AFF');
      ctx.fill();

      // 绘制数值
      ctx.setFillStyle('#333333');
      ctx.fillText(item.count.toString(), x, barY - 10);
    });

    // 绘制Y轴标签
    ctx.setTextAlign('right');
    const yStep = maxValue / 5;

    for (let i = 0; i <= 5; i++) {
      const y = height - padding.bottom - (i / 5) * chartHeight;
      const value = Math.round(i * yStep);

      ctx.fillText(value.toString(), padding.left - 10, y + 5);

      // 绘制网格线
      ctx.beginPath();
      ctx.setLineWidth(0.5);
      ctx.setLineDash([5, 5]);
      ctx.moveTo(padding.left, y);
      ctx.lineTo(width - padding.right, y);
      ctx.stroke();
      ctx.setLineDash([]);
    }

    // 绘制标题
    ctx.setFontSize(16);
    ctx.setFillStyle('#000000');
    ctx.setTextAlign('center');
    ctx.fillText('居民年龄分布', width / 2, 20);

    ctx.draw();

    // 更新图表渲染状态
    const chartsRendered = this.data.chartsRendered;
    chartsRendered.ageDistribution = true;
    this.setData({
      chartsRendered: chartsRendered
    });
  },

  // 渲染房屋类型分布图表（饼图）
  renderHouseTypeChart: function() {
    const ctx = wx.createCanvasContext('houseTypeChart');
    const { houseTypeDistribution } = this.data.statistics;
    const total = houseTypeDistribution.reduce((sum, item) => sum + item.count, 0);

    // 定义颜色
    const colors = ['#FF8C00', '#007AFF', '#34C759', '#FF3B30', '#5856D6'];

    // 绘制饼图
    let startAngle = 0;
    const data = houseTypeDistribution.map((item, index) => ({
      ...item,
      color: colors[index % colors.length]
    }));

    const width = this.data.chartConfig.houseType.width;
    const height = this.data.chartConfig.houseType.height;
    const centerX = width / 2;
    const centerY = height / 2;
    const radius = Math.min(centerX, centerY) - 80;

    data.forEach((item, index) => {
      const portion = item.count / total;
      const endAngle = startAngle + portion * 2 * Math.PI;

      // 绘制扇形
      ctx.beginPath();
      ctx.moveTo(centerX, centerY);
      ctx.arc(centerX, centerY, radius, startAngle, endAngle);
      ctx.setFillStyle(item.color);
      ctx.fill();

      // 计算标签位置
      const midAngle = startAngle + (endAngle - startAngle) / 2;
      const labelRadius = radius * 0.7;
      const labelX = centerX + labelRadius * Math.cos(midAngle);
      const labelY = centerY + labelRadius * Math.sin(midAngle);

      // 绘制标签
      ctx.setFontSize(14);
      ctx.setFillStyle('#FFFFFF');
      ctx.setTextAlign('center');
      ctx.setTextBaseline('middle');
      ctx.fillText(`${Math.round(portion * 100)}%`, labelX, labelY);

      startAngle = endAngle;
    });

    // 绘制图例
    const legendX = 30;
    let legendY = height - 120;

    data.forEach((item, index) => {
      // 绘制颜色方块
      ctx.beginPath();
      ctx.rect(legendX, legendY, 15, 15);
      ctx.setFillStyle(item.color);
      ctx.fill();

      // 绘制文字
      ctx.setFontSize(12);
      ctx.setFillStyle('#333333');
      ctx.setTextAlign('left');
      ctx.fillText(`${item.type}: ${item.count}户`, legendX + 25, legendY + 8);

      legendY += 25;
    });

    // 绘制标题
    ctx.setFontSize(16);
    ctx.setFillStyle('#000000');
    ctx.setTextAlign('center');
    ctx.fillText('房屋类型分布', width / 2, 20);

    ctx.draw();

    // 更新图表渲染状态
    const chartsRendered = this.data.chartsRendered;
    chartsRendered.houseType = true;
    this.setData({
      chartsRendered: chartsRendered
    });
  },

  // 渲染楼栋分布图表（柱状图）
  renderBuildingDistributionChart: function() {
    const ctx = wx.createCanvasContext('buildingDistributionChart');
    const { buildingDistribution } = this.data.statistics;

    const width = this.data.chartConfig.buildingDistribution.width;
    const height = this.data.chartConfig.buildingDistribution.height;
    const padding = { top: 40, right: 20, bottom: 60, left: 40 };

    const chartWidth = width - padding.left - padding.right;
    const chartHeight = height - padding.top - padding.bottom;

    // 计算最大值
    const maxValue = Math.max(...buildingDistribution.map(item => item.count)) * 1.2;

    // 绘制坐标轴
    ctx.beginPath();
    ctx.setLineWidth(1);
    ctx.setStrokeStyle('#CCCCCC');

    // X轴
    ctx.moveTo(padding.left, height - padding.bottom);
    ctx.lineTo(width - padding.right, height - padding.bottom);

    // Y轴
    ctx.moveTo(padding.left, padding.top);
    ctx.lineTo(padding.left, height - padding.bottom);
    ctx.stroke();

    // 绘制X轴标签
    ctx.setFontSize(12);
    ctx.setFillStyle('#666666');
    ctx.setTextAlign('center');

    const barWidth = chartWidth / buildingDistribution.length * 0.6;
    const barSpacing = chartWidth / buildingDistribution.length;

    buildingDistribution.forEach((item, index) => {
      const x = padding.left + barSpacing * (index + 0.5);
      ctx.fillText(item.building, x, height - padding.bottom + 20);

      // 绘制柱状
      const barHeight = (item.count / maxValue) * chartHeight;
      const barX = x - barWidth / 2;
      const barY = height - padding.bottom - barHeight;

      ctx.beginPath();
      ctx.rect(barX, barY, barWidth, barHeight);
      ctx.setFillStyle('#34C759');
      ctx.fill();

      // 绘制数值
      ctx.setFillStyle('#333333');
      ctx.fillText(item.count.toString(), x, barY - 10);
    });

    // 绘制Y轴标签
    ctx.setTextAlign('right');
    const yStep = maxValue / 5;

    for (let i = 0; i <= 5; i++) {
      const y = height - padding.bottom - (i / 5) * chartHeight;
      const value = Math.round(i * yStep);

      ctx.fillText(value.toString(), padding.left - 10, y + 5);

      // 绘制网格线
      ctx.beginPath();
      ctx.setLineWidth(0.5);
      ctx.setLineDash([5, 5]);
      ctx.moveTo(padding.left, y);
      ctx.lineTo(width - padding.right, y);
      ctx.stroke();
      ctx.setLineDash([]);
    }

    // 绘制标题
    ctx.setFontSize(16);
    ctx.setFillStyle('#000000');
    ctx.setTextAlign('center');
    ctx.fillText('楼栋居民分布', width / 2, 20);

    ctx.draw();

    // 更新图表渲染状态
    const chartsRendered = this.data.chartsRendered;
    chartsRendered.buildingDistribution = true;
    this.setData({
      chartsRendered: chartsRendered
    });
  },

  // 导出统计数据
  exportStatistics: function() {
    wx.showToast({
      title: '导出功能开发中',
      icon: 'none'
    });
  },

  // 下拉刷新
  onPullDownRefresh: function() {
    // 重新加载数据
    this.loadData();

    // 停止下拉刷新
    wx.stopPullDownRefresh();
  },

  // 导出统计数据
  exportStatistics: function() {
    wx.showToast({
      title: '导出功能开发中',
      icon: 'none'
    });
  }
})