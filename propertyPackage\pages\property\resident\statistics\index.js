// pages/property/resident/statistics/index.js
const util = require('../../../../../utils/util.js')
const propertyApi = require('../../../../../api/propertyApi.js')

Page({
  data: {
    // 统计数据
    statistics: {
      genderCount: {},
      buildingResidentCount: {},
      ageCount: {},
      typeCount: {},
      trendCount: [],
      roomTypeCount: {}
    },

    // 当前激活的标签
    activeTab: 'type',

    // 是否正在加载
    isLoading: false,

    // 字典数据
    residentTypeDict: [],
    genderDict: [],
    roomTypeDict: []
  },

  onLoad: function() {
    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '居民统计'
    });

    // 初始化字典数据
    this.initDictData();

    // 加载数据
    this.loadData();
  },

  // 初始化字典数据
  initDictData: function() {
    try {
      // 获取居民类型字典
      const residentTypeDict = util.getDictByNameEn('resident_type');
      const residentTypeOptions = residentTypeDict && residentTypeDict.length > 0 && residentTypeDict[0].children ? residentTypeDict[0].children : [];

      // 获取性别字典
      const genderDict = util.getDictByNameEn('gender');
      const genderOptions = genderDict && genderDict.length > 0 && genderDict[0].children ? genderDict[0].children : [];

      // 获取房屋类型字典
      const roomTypeDict = util.getDictByNameEn('room_type');
      const roomTypeOptions = roomTypeDict && roomTypeDict.length > 0 && roomTypeDict[0].children ? roomTypeDict[0].children : [];

      this.setData({
        residentTypeDict: residentTypeOptions,
        genderDict: genderOptions,
        roomTypeDict: roomTypeOptions
      });

      console.log('字典数据初始化完成:', {
        residentTypeDict: residentTypeOptions,
        genderDict: genderOptions,
        roomTypeDict: roomTypeOptions
      });
    } catch (error) {
      console.error('初始化字典数据失败:', error);
    }
  },

  // 加载数据
  loadData: function() {
    this.setData({
      isLoading: true
    });

    const selectedCommunity = wx.getStorageSync('selectedCommunity');
    if (!selectedCommunity || !selectedCommunity.id) {
      console.error('未选择小区');
      wx.showToast({
        title: '请先选择小区',
        icon: 'none'
      });
      this.setData({ isLoading: false });
      return;
    }

    const params = {
      communityId: selectedCommunity.id
    };

    console.log('加载居民统计数据，参数:', params);

    propertyApi.getResidentStatistics(params)
      .then(res => {
        console.log('居民统计API响应:', res);
        if (res && res.data) {
          this.processStatisticsData(res.data);
        } else {
          console.log('居民统计数据为空');
          this.setData({ isLoading: false });
        }
      })
      .catch(error => {
        console.error('获取居民统计数据失败:', error);
        wx.showToast({
          title: '获取数据失败',
          icon: 'none'
        });
        this.setData({ isLoading: false });
      });
  },

  // 处理统计数据
  processStatisticsData: function(data) {
    console.log('处理统计数据:', data);

    // 处理居民类型分布数据
    const typeData = this.processTypeData(data.typeCount || {});

    // 处理性别分布数据
    const genderData = this.processGenderData(data.genderCount || {});

    // 处理年龄分布数据
    const ageData = this.processAgeData(data.ageCount || {});

    // 处理房屋类型分布数据
    const roomTypeData = this.processRoomTypeData(data.roomTypeCount || {});

    // 处理楼栋分布数据
    const buildingData = this.processBuildingData(data.buildingResidentCount || {});

    // 处理趋势数据
    const trendData = data.trendCount || [];

    // 计算年度总数
    const yearlyTotal = trendData.reduce((sum, count) => sum + count, 0);

    this.setData({
      'statistics.typeCount': data.typeCount,
      'statistics.genderCount': data.genderCount,
      'statistics.ageCount': data.ageCount,
      'statistics.roomTypeCount': data.roomTypeCount,
      'statistics.buildingResidentCount': data.buildingResidentCount,
      'statistics.trendCount': trendData,
      'statistics.yearlyTotal': yearlyTotal,
      'statistics.typeData': typeData,
      'statistics.genderData': genderData,
      'statistics.ageData': ageData,
      'statistics.roomTypeData': roomTypeData,
      'statistics.buildingData': buildingData,
      isLoading: false
    });

    console.log('统计数据处理完成');
  },

  // 处理居民类型分布数据
  processTypeData: function(typeCount) {
    const typeData = [];
    const residentTypeDict = this.data.residentTypeDict;

    Object.keys(typeCount).forEach(key => {
      const count = typeCount[key];
      const dictItem = residentTypeDict.find(item => item.nameEn === key);
      const name = dictItem ? dictItem.nameCn : key;

      typeData.push({
        name: name,
        value: count,
        nameEn: key
      });
    });

    return typeData;
  },

  // 处理性别分布数据
  processGenderData: function(genderCount) {
    const genderData = [];
    const genderDict = this.data.genderDict;

    Object.keys(genderCount).forEach(key => {
      const count = genderCount[key];
      const dictItem = genderDict.find(item => item.nameEn === key);
      const name = dictItem ? dictItem.nameCn : key;

      genderData.push({
        name: name,
        value: count,
        nameEn: key
      });
    });

    return genderData;
  },

  // 处理年龄分布数据
  processAgeData: function(ageCount) {
    const ageData = [];
    const ageMapping = {
      'under20': '20岁以下',
      'age20to30': '20-30岁',
      'age30to40': '30-40岁',
      'age40to50': '40-50岁',
      'over50': '50岁以上'
    };

    Object.keys(ageCount).forEach(key => {
      const count = ageCount[key];
      const name = ageMapping[key] || key;

      ageData.push({
        name: name,
        value: count,
        nameEn: key
      });
    });

    return ageData;
  },

  // 处理房屋类型分布数据
  processRoomTypeData: function(roomTypeCount) {
    const roomTypeData = [];
    const roomTypeDict = this.data.roomTypeDict;

    Object.keys(roomTypeCount).forEach(key => {
      const count = roomTypeCount[key];
      const dictItem = roomTypeDict.find(item => item.nameEn === key);
      const name = dictItem ? dictItem.nameCn : key;

      roomTypeData.push({
        name: name,
        value: count,
        nameEn: key
      });
    });

    return roomTypeData;
  },

  // 处理楼栋分布数据
  processBuildingData: function(buildingResidentCount) {
    const buildingData = [];

    Object.keys(buildingResidentCount).forEach(key => {
      const count = buildingResidentCount[key];

      buildingData.push({
        name: key,
        value: count
      });
    });

    return buildingData;
  },

  // 切换标签
  switchTab: function(e) {
    const tab = e.currentTarget.dataset.tab;

    if (this.data.activeTab !== tab) {
      this.setData({
        activeTab: tab
      });
    }
  },

  // 下拉刷新
  onPullDownRefresh: function() {
    // 重新加载数据
    this.loadData();

    // 停止下拉刷新
    wx.stopPullDownRefresh();
  },

  // 导出统计数据
  exportStatistics: function() {
    wx.showToast({
      title: '导出功能开发中',
      icon: 'none'
    });
  }
})