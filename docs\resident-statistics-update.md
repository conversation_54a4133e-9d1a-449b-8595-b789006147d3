# 居民统计页面修改说明

## 修改内容

### 1. 去掉月度周度功能
- 移除了时间范围选择器（月度/周度切换）
- 删除了相关的切换逻辑和数据处理

### 2. 去掉概览里的居民总体情况
- 移除了"居民总体情况"卡片，包括总人数、已认证、未认证等统计
- 重新组织标签页结构

### 3. 使用接口数据
- 使用 `propertyApi.getResidentStatistics(params)` 接口获取数据
- 参数：`{ communityId: wx.getStorageSync('selectedCommunity').id }`

## 新的标签页结构

1. **居民类型** - 显示业主、租户、家属等类型分布
2. **性别分布** - 显示男女性别统计
3. **年龄分布** - 显示各年龄段分布
4. **趋势分析** - 显示近12个月居民增长趋势
5. **房屋分析** - 显示房屋类型分布和楼栋居民分布

## 数据处理

### 字典映射
- 居民类型：使用 `util.getDictByNameEn('resident_type')[0].children`
- 性别：使用 `util.getDictByNameEn('gender')[0].children`
- 房屋类型：使用 `util.getDictByNameEn('room_type')[0].children`

### 年龄分布映射
```javascript
const ageMapping = {
  'under20': '20岁以下',
  'age20to30': '20-30岁',
  'age30to40': '30-40岁',
  'age40to50': '40-50岁',
  'over50': '50岁以上'
};
```

## 接口数据结构

```javascript
{
  "genderCount": {
    "woman": 0,
    "man": 2
  },
  "buildingResidentCount": {
    "2栋": 2,
    "1栋": 6
  },
  "ageCount": {
    "age40to50": 0,
    "over50": 0,
    "under20": 0,
    "age20to30": 0,
    "age30to40": 2
  },
  "typeCount": {
    "owner": 3,
    "family": 4,
    "tenant": 1
  },
  "trendCount": [0,0,0,0,0,0,0,0,0,0,6,1],
  "roomTypeCount": {
    "normal_room3": 1,
    "normal_room4": 1,
    "normal_room5": 1,
    "loft": 1,
    "single_room": 2,
    "normal_room1": 1,
    "villa": 5,
    "duplex_loft": 4,
    "apartment": 1,
    "normal_room2": 1
  }
}
```

## 主要功能

1. **数据加载**：页面加载时自动获取统计数据
2. **字典映射**：将接口返回的英文字段映射为中文显示
3. **图表展示**：使用简单的CSS图表展示数据分布
4. **下拉刷新**：支持下拉刷新重新加载数据
5. **导出功能**：预留导出统计数据功能（开发中）

## 技术要点

- 严格按照接口返回的数据结构进行处理
- 不添加自定义字段，完全使用接口数据
- 使用字典进行数据映射，确保显示的一致性
- 错误处理：网络错误、数据为空等情况的处理
