<!--居民搜索页面-->
<view class="container">
  <!-- 搜索栏 -->
  <view class="search-bar">
    <view class="search-input-wrap">
      <view class="search-icon"></view>
      <input class="search-input" type="text" placeholder="搜索姓名、手机号、房号等" value="{{searchText}}" bindinput="onSearchInput" />
      <view class="clear-icon" bindtap="clearSearch" wx:if="{{searchText}}"></view>
    </view>
    <view class="filter-btn" bindtap="showAdvancedFilter">
      <view class="filter-icon"></view>
    </view>
  </view>

  <!-- 快速筛选条件 -->
  <view class="filter-tags">
    <view class="tag {{filter.active ? 'active' : ''}}" wx:for="{{filters}}" wx:key="id" wx:for-item="filter" bindtap="toggleFilter" data-id="{{filter.id}}">
      {{filter.name}}
    </view>
  </view>

  <!-- 居民列表 -->
  <view class="resident-list">
    <block wx:if="{{residents.length > 0}}">
      <view class="resident-card" wx:for="{{residents}}" wx:key="id" wx:for-index="index" data-index="{{index}}"  style="{{item.style}}">
        <view class="resident-content" bindtap="viewResidentDetail" data-id="{{item.id}}">
          <view class="resident-header">
            <view class="resident-name">{{item.name}}</view>
            <view class="resident-badges">
              <view class="badge {{item.type}}">{{item.typeText}}</view>
              <view class="badge {{item.status}}">{{item.statusText}}</view>
            </view>
          </view>

          <view class="resident-info">
            <view class="info-row">
              <view class="info-label">手机号</view>
              <view class="info-value">{{item.phone}}</view>
            </view>
            <view class="info-row">
              <view class="info-label">身份证</view>
              <view class="info-value">{{item.idNumber}}</view>
            </view>
            <view class="info-row">
              <view class="info-label">房屋</view>
              <view class="info-value">{{item.address}}</view>
            </view>
            <view class="info-row">
              <view class="info-label">注册时间</view>
              <view class="info-value">{{item.registerTime}}</view>
            </view>
          </view>
        </view>

        <!-- 滑动显示的操作按钮 -->
        <view class="slide-actions">
          <view class="action-view" catchtap="viewResidentDetail" data-id="{{item.id}}">详情</view>
        </view>
      </view>
    </block>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{residents.length === 0}}">
      <view class="empty-icon"></view>
      <view class="empty-text">暂无符合条件的居民信息</view>
    </view>
  </view>

  <!-- 加载更多 -->
  <view class="load-more" wx:if="{{hasMore}}">
    <view class="loading-indicator" wx:if="{{isLoading}}"></view>
    <text wx:else bindtap="loadMore">加载更多</text>
  </view>

  <!-- 扫码按钮 -->
  <view class="scan-btn" bindtap="scanQRCode">
    <view class="scan-icon"></view>
    <text>扫码查询</text>
  </view>

  <!-- 高级筛选弹窗 -->
  <view class="advanced-filter {{showAdvancedFilter ? 'show' : ''}}" catchtouchmove="preventTouchMove">
    <view class="filter-mask" bindtap="hideAdvancedFilter"></view>
    <view class="filter-content">
      <view class="filter-header">
        <text class="filter-title">高级筛选</text>
        <view class="filter-close" bindtap="hideAdvancedFilter"></view>
      </view>

      <view class="filter-body">
        <!-- 楼栋筛选 -->
        <view class="filter-section">
          <view class="section-title">楼栋</view>
          <view class="option-list">
            <view class="option {{advancedFilters.selectedBuilding === option ? 'active' : ''}}"
                  wx:for="{{advancedFilters.buildingOptions}}"
                  wx:key="*this"
                  wx:for-item="option"
                  bindtap="selectAdvancedFilter"
                  data-type="building"
                  data-value="{{option}}">
              {{option}}
            </view>
          </view>
        </view>

        <!-- 时间筛选 -->
        <view class="filter-section">
          <view class="section-title">注册时间</view>
          <view class="option-list">
            <view class="option {{advancedFilters.selectedTime === option ? 'active' : ''}}"
                  wx:for="{{advancedFilters.timeOptions}}"
                  wx:key="*this"
                  wx:for-item="option"
                  bindtap="selectAdvancedFilter"
                  data-type="time"
                  data-value="{{option}}">
              {{option}}
            </view>
          </view>
        </view>

        <!-- 状态筛选 -->
        <view class="filter-section">
          <view class="section-title">状态</view>
          <view class="option-list">
            <view class="option {{advancedFilters.selectedStatus === option ? 'active' : ''}}"
                  wx:for="{{advancedFilters.statusOptions}}"
                  wx:key="*this"
                  wx:for-item="option"
                  bindtap="selectAdvancedFilter"
                  data-type="status"
                  data-value="{{option}}">
              {{option}}
            </view>
          </view>
        </view>
      </view>

      <view class="filter-footer">
        <button class="btn-reset" bindtap="resetAdvancedFilter">重置</button>
        <button class="btn-apply" bindtap="applyAdvancedFilter">应用</button>
      </view>
    </view>
  </view>
</view>
