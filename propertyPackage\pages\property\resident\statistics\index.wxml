<view class="resident-stats-container">
  <!-- 标题栏 -->
  <view class="stats-header">
    <view class="stats-title">居民统计分析</view>
    <view class="stats-actions">
      <view class="stats-action" bindtap="exportStatistics">
        <text>导出</text>
      </view>
    </view>
  </view>

  <!-- 标签页切换 -->
  <view class="tab-header">
    <view class="tab-item {{activeTab === 'type' ? 'active' : ''}}" bindtap="switchTab" data-tab="type">
      <text>居民类型</text>
    </view>
    <view class="tab-item {{activeTab === 'gender' ? 'active' : ''}}" bindtap="switchTab" data-tab="gender">
      <text>性别分布</text>
    </view>
    <view class="tab-item {{activeTab === 'age' ? 'active' : ''}}" bindtap="switchTab" data-tab="age">
      <text>年龄分布</text>
    </view>
    <view class="tab-item {{activeTab === 'trend' ? 'active' : ''}}" bindtap="switchTab" data-tab="trend">
      <text>趋势分析</text>
    </view>
    <view class="tab-item {{activeTab === 'house' ? 'active' : ''}}" bindtap="switchTab" data-tab="house">
      <text>房屋分析</text>
    </view>
  </view>

  <!-- 加载中 -->
  <view class="loading" wx:if="{{isLoading}}">
    <view class="loading-spinner"></view>
    <text>加载中...</text>
  </view>

  <!-- 居民类型标签页 -->
  <view class="tab-content" wx:if="{{activeTab === 'type' && !isLoading}}">
    <view class="stats-card">
      <view class="card-header">
        <text class="card-title">居民类型分布</text>
      </view>
      <view class="card-body">
        <view class="summary-info">
          <view wx:for="{{statistics.typeData}}" wx:key="nameEn" class="summary-item">
            <text class="summary-value">{{item.value}}</text>
            <text class="summary-label">{{item.name}}</text>
          </view>
        </view>
        <view class="chart-container">
          <!-- 居民类型分布饼图 -->
          <view class="mock-chart pie-chart">
            <view class="pie-segments">
              <view wx:for="{{statistics.typeData}}" wx:key="nameEn" class="pie-segment" style="transform: rotate({{index * (360 / statistics.typeData.length)}}deg); background-color: {{['#FF8C00', '#007AFF', '#34C759', '#FF3B30', '#5856D6'][index]}};"></view>
            </view>
            <view class="pie-center"></view>
          </view>
          <view class="chart-legend">
            <view wx:for="{{statistics.typeData}}" wx:key="nameEn" class="legend-item">
              <view class="legend-color" style="background-color: {{['#FF8C00', '#007AFF', '#34C759', '#FF3B30', '#5856D6'][index]}};"></view>
              <text class="legend-label">{{item.name}}</text>
              <text class="legend-value">{{item.value}}人</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 性别分布标签页 -->
  <view class="tab-content" wx:if="{{activeTab === 'gender' && !isLoading}}">
    <view class="stats-card">
      <view class="card-header">
        <text class="card-title">性别分布</text>
      </view>
      <view class="card-body">
        <view class="summary-info">
          <view wx:for="{{statistics.genderData}}" wx:key="nameEn" class="summary-item">
            <text class="summary-value">{{item.value}}</text>
            <text class="summary-label">{{item.name}}</text>
          </view>
        </view>
        <view class="chart-container">
          <!-- 性别分布图表 -->
          <view class="gender-chart">
            <view wx:for="{{statistics.genderData}}" wx:key="nameEn" class="gender-bar-container">
              <view class="gender-label">{{item.name}}</view>
              <view class="gender-bar-wrapper">
                <view class="gender-bar {{item.nameEn === 'man' ? 'male' : 'female'}}" style="width: {{item.value * 100 / (statistics.genderData[0].value + statistics.genderData[1].value)}}%;"></view>
                <text class="gender-value">{{item.value}}人</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 趋势分析标签页 -->
  <view class="tab-content" wx:if="{{activeTab === 'trend' && !isLoading}}">
    <view class="stats-card">
      <view class="card-header">
        <text class="card-title">居民增长趋势</text>
        <text class="card-subtitle">近12个月数据</text>
      </view>
      <view class="card-body">
        <view class="summary-info">
          <view class="summary-item">
            <text class="summary-value">{{statistics.trendCount[11] || 0}}</text>
            <text class="summary-label">本月新增</text>
          </view>
          <view class="summary-item">
            <text class="summary-value">{{statistics.yearlyTotal || 0}}</text>
            <text class="summary-label">年度新增</text>
          </view>
        </view>
        <view class="chart-container">
          <!-- 月度新增居民趋势图 -->
          <view class="mock-chart trend-chart">
            <view class="chart-y-axis">
              <text wx:for="{{[50,40,30,20,10,0]}}" wx:key="index">{{item}}</text>
            </view>
            <view class="chart-content">
              <view class="chart-bars">
                <view wx:for="{{statistics.trendCount}}" wx:key="index" class="chart-bar" style="height: {{item * 2}}rpx;"></view>
              </view>
              <view class="chart-x-axis">
                <text wx:for="{{['1月','2月','3月','4月','5月','6月','7月','8月','9月','10月','11月','12月']}}" wx:key="index" class="x-label">{{item}}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 年龄分布标签页 -->
  <view class="tab-content" wx:if="{{activeTab === 'age' && !isLoading}}">
    <view class="stats-card">
      <view class="card-header">
        <text class="card-title">居民年龄分布</text>
      </view>
      <view class="card-body">
        <view class="chart-container">
          <!-- 年龄分布图表 -->
          <view class="age-chart">
            <view wx:for="{{statistics.ageData}}" wx:key="nameEn" class="age-item">
              <view class="age-bar-container">
                <view class="age-label">{{item.name}}</view>
                <view class="age-bar-wrapper">
                  <view class="age-bar" style="width: {{item.percent}}%; background-color: {{['#4f46e5', '#3b82f6', '#60a5fa', '#93c5fd', '#bfdbfe'][index]}};"></view>
                  <text class="age-value">{{item.value}}人 ({{item.percent}}%)</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 房屋分析标签页 -->
  <view class="tab-content" wx:if="{{activeTab === 'house' && !isLoading}}">
    <view class="stats-card">
      <view class="card-header">
        <text class="card-title">房屋类型分布</text>
      </view>
      <view class="card-body">
        <view class="chart-container">
          <!-- 房屋类型分布图表 -->
          <view class="mock-chart donut-chart">
            <view class="donut-segments">
              <view wx:for="{{statistics.roomTypeData}}" wx:key="nameEn" class="donut-segment" style="transform: rotate({{index * (360 / statistics.roomTypeData.length)}}deg); background-color: {{['#FF8C00', '#007AFF', '#34C759', '#FF3B30', '#5856D6', '#FF9500', '#30D158', '#007AFF', '#5856D6', '#FF3B30'][index]}};"></view>
            </view>
            <view class="donut-center"></view>
          </view>
          <view class="chart-legend">
            <view wx:for="{{statistics.roomTypeData}}" wx:key="nameEn" class="legend-item">
              <view class="legend-color" style="background-color: {{['#FF8C00', '#007AFF', '#34C759', '#FF3B30', '#5856D6', '#FF9500', '#30D158', '#007AFF', '#5856D6', '#FF3B30'][index]}};"></view>
              <text class="legend-label">{{item.name}}</text>
              <text class="legend-value">{{item.value}}户</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <view class="stats-card">
      <view class="card-header">
        <text class="card-title">楼栋居民分布</text>
      </view>
      <view class="card-body">
        <view class="chart-container">
          <!-- 楼栋分布图表 -->
          <view class="mock-chart trend-chart">
            <view class="chart-y-axis">
              <text wx:for="{{[60,50,40,30,20,10,0]}}" wx:key="index">{{item}}</text>
            </view>
            <view class="chart-content">
              <view class="chart-bars">
                <view wx:for="{{statistics.buildingData}}" wx:key="name" class="chart-bar" style="height: {{item.value * 2}}rpx; background-color: #34C759;"></view>
              </view>
              <view class="chart-x-axis">
                <text wx:for="{{statistics.buildingData}}" wx:key="name" class="x-label">{{item.name}}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>