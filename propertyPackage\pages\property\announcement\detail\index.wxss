/* 公告详情页样式 */

.container {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding-bottom: 48rpx;
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #ff8c00;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

/* 公告详情 */
.announcement-detail {
  padding: 32rpx;
  background-color: #fff;
  margin: 32rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

/* 公告标题 */
.announcement-title {
  font-size: 40rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.4;
  margin-bottom: 24rpx;
}

/* 公告信息 */
.announcement-info {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 32rpx;
  padding-bottom: 32rpx;
  border-bottom: 1rpx solid #eee;
}

.info-item {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #666;
  margin-right: 32rpx;
  margin-bottom: 16rpx;
}

.type-icon, .time-icon, .read-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 8rpx;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
}

.type-icon.property_notice {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%234080FF' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9'%3E%3C/path%3E%3Cpath d='M13.73 21a2 2 0 0 1-3.46 0'%3E%3C/path%3E%3C/svg%3E");
}

.type-icon.activity_notice {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%2352C41A' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolygon points='11 5 6 9 2 9 2 15 6 15 11 19 11 5'%3E%3C/polygon%3E%3Cpath d='M19.07 4.93a10 10 0 0 1 0 14.14M15.54 8.46a5 5 0 0 1 0 7.07'%3E%3C/path%3E%3C/svg%3E");
}

.type-icon.emergency_notice {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23F5222D' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z'%3E%3C/path%3E%3Cline x1='12' y1='9' x2='12' y2='13'%3E%3C/line%3E%3Cline x1='12' y1='17' x2='12.01' y2='17'%3E%3C/line%3E%3C/svg%3E");
}

.time-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23999999' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cpolyline points='12 6 12 12 16 14'%3E%3C/polyline%3E%3C/svg%3E");
}

.read-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23999999' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z'%3E%3C/path%3E%3Ccircle cx='12' cy='12' r='3'%3E%3C/circle%3E%3C/svg%3E");
}

/* 公告内容 */
.announcement-content {
  font-size: 32rpx;
  color: #333;
  line-height: 1.6;
  margin-bottom: 32rpx;
  word-wrap: break-word;
  word-break: break-all;
}

/* 富文本内容样式 */
.announcement-content rich-text {
  width: 100%;
}

/* 图片展示 */
.image-gallery {
  margin-top: 32rpx;
}

.image-item {
  margin-bottom: 16rpx;
  border-radius: 8rpx;
  overflow: hidden;
}

.image-item .image {
  width: 100%;
  display: block;
}

/* 预览模式提示 */
.preview-tip {
  margin-top: 48rpx;
  text-align: center;
  padding: 16rpx 0;
  background-color: rgba(255, 140, 0, 0.1);
  border-radius: 8rpx;
}

.preview-tip .tip-text {
  font-size: 28rpx;
  color: #ff8c00;
}

/* 错误状态 */
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
}

.error-icon {
  width: 120rpx;
  height: 120rpx;
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23CCCCCC' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cline x1='12' y1='8' x2='12' y2='12'%3E%3C/line%3E%3Cline x1='12' y1='16' x2='12.01' y2='16'%3E%3C/line%3E%3C/svg%3E") no-repeat center;
  background-size: contain;
  margin-bottom: 24rpx;
}

.error-text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 32rpx;
}

.error-btn {
  padding: 16rpx 48rpx;
  background-color: #ff8c00;
  color: #fff;
  font-size: 28rpx;
  border-radius: 8rpx;
}

/* 暗黑模式 */
.dark-mode {
  background-color: #1f1f1f;
  color: #fff;
}

.dark-mode .announcement-detail {
  background-color: #2d2d2d;
}

.dark-mode .announcement-title {
  color: #fff;
}

.dark-mode .announcement-info {
  border-bottom-color: #3d3d3d;
}

.dark-mode .info-item {
  color: #aaa;
}

.dark-mode .announcement-content {
  color: #ddd;
}
