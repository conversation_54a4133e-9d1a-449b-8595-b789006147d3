// pages/property/workorder/list/index.js
const util = require('@/utils/util.js');
const workOrderApi = require('@/api/workOrderApi.js');
const animationHelper = require('@/utils/animation-helper.js');

Page({
  data: {
    darkMode: false,
    orders: [],
    filteredOrders: [],
    loading: true,
    currentPage: 1,
    pageSize: 10,
    hasMore: true,
    searchValue: '',
    currentStatus: '',
    currentType: '',
    statusOptions: [
      { value: '', label: '全部' },
      { value: 'pending', label: '待处理' },
      { value: 'processing', label: '处理中' },
      { value: 'completed', label: '已完成' },
      { value: 'cancelled', label: '已取消' }
    ],
    typeOptions: [], // 工单类型选项，从字典获取
    sortOptions: [
      { value: 'time-desc', label: '最新优先' },
      { value: 'time-asc', label: '最早优先' },
      { value: 'priority-desc', label: '优先级高到低' },
      { value: 'priority-asc', label: '优先级低到高' }
    ],
    currentSort: 'time-desc',
    showFilterPanel: false,
    selectedOrders: [],
    isSelectMode: false,
    batchActionOptions: [
      { value: 'process', label: '批量处理' },
      { value: 'assign', label: '批量分配' },
      { value: 'complete', label: '批量完成' },
      { value: 'cancel', label: '批量取消' }
    ],
    // 批量操作按钮状态控制
    canProcess: false,
    canComplete: false,
    canCancel: false,
    // 动画相关
    filterPanelAnimation: {},
    batchToolbarAnimation: {},
    statusTabAnimation: {}
  },

  onLoad: function(options) {
    // 检查暗黑模式
    const app = getApp();
    if (app.globalData && app.globalData.darkMode !== undefined) {
      this.setData({
        darkMode: app.globalData.darkMode
      });
    }

    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '工单列表'
    });

    // 初始化工单类型字典
    this.initTypeOptions();

    // 获取URL参数中的状态
    if (options.status) {
      this.setData({
        currentStatus: options.status
      });
    }

    // 获取URL参数中的类型
    if (options.type) {
      this.setData({
        currentType: options.type
      });
    }

    // 初始化工单类型选项
    this.initTypeOptions();

    // 加载工单数据
    this.loadWorkOrders();
  },

  // 初始化工单类型选项
  initTypeOptions: function() {
    try {
      const workOrderTypeDict = util.getDictByNameEn('work_order_type')[0].children;
      const typeOptions = [
        { value: '', label: '全部类型' },
        ...workOrderTypeDict.map(item => ({
          value: item.nameEn,
          label: item.nameCn
        }))
      ];

      this.setData({
        typeOptions
      });
    } catch (error) {
      console.error('初始化工单类型字典失败', error);
      // 设置默认类型选项
      this.setData({
        typeOptions: [
          { value: '', label: '全部类型' },
          { value: 'repair', label: '维修工单' },
          { value: 'complaint', label: '投诉工单' },
          { value: 'suggestion', label: '建议工单' }
        ]
      });
    }
  },

  onShow: function() {
    // 页面显示时刷新数据
    this.loadWorkOrders();
  },

  onPullDownRefresh: function() {
    // 下拉刷新
    this.setData({
      currentPage: 1,
      hasMore: true,
      orders: [],
      filteredOrders: []
    });
    this.loadWorkOrders().then(() => {
      wx.stopPullDownRefresh();
    }).catch(() => {
      wx.stopPullDownRefresh();
    });
  },

  onReachBottom: function() {
    // 上拉加载更多
    if (this.data.hasMore && !this.data.loading) {
      this.loadMoreOrders();
    }
  },

  // 加载工单数据
  loadWorkOrders: function() {
    this.setData({ loading: true });

    // 构建查询参数
    const params = {
      page: this.data.currentPage,
      size: this.data.pageSize
    };

    // 添加筛选条件
    if (this.data.currentStatus) {
      params.status = this.data.currentStatus;
    }
    if (this.data.currentType) {
      params.type = this.data.currentType;
    }
    if (this.data.searchValue) {
      params.keyword = this.data.searchValue;
    }

    return workOrderApi.getPropertyWorkOrderList(params)
      .then(response => {
        const orders = response.list || [];
        console.log('获取到工单数据:', orders.length, '条');

        // 处理工单数据
        const processedOrders = orders.map(order => {
          // 标记新工单（24小时内创建的工单）
          const now = new Date();
          const twentyFourHoursAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
          const createTime = new Date(order.createTime);

          return {
            ...order,
            isNew: createTime > twentyFourHoursAgo,
            // 获取状态显示文本
            statusText: this.getStatusText(order.status),
            // 获取类型显示文本
            typeText: this.getTypeText(order.type),
            // 处理图片字段
            imageList: order.media ? order.media.split(',').filter(img => img.trim()) : []
          };
        });

        // 根据当前排序条件排序工单
        const sortedOrders = this.sortOrders(processedOrders);

        console.log('处理后工单数据:', sortedOrders.length, '条');

        // 判断是否还有更多数据
        const hasMore = response.total > this.data.currentPage * this.data.pageSize;

        this.setData({
          orders: this.data.currentPage === 1 ? sortedOrders : [...this.data.orders, ...sortedOrders],
          filteredOrders: this.data.currentPage === 1 ? sortedOrders : [...this.data.filteredOrders, ...sortedOrders],
          hasMore: hasMore,
          loading: false
        });

        console.log('列表数据已更新，当前状态:', {
          totalOrders: response.total || 0,
          currentPageCount: sortedOrders.length,
          currentPage: this.data.currentPage,
          hasMore: hasMore
        });
      })
      .catch(error => {
        console.error('加载工单数据失败', error);
        this.setData({ loading: false });

        wx.showToast({
          title: '加载失败，请重试',
          icon: 'none'
        });
      });
  },

  // 加载更多工单
  loadMoreOrders: function() {
    if (!this.data.hasMore || this.data.loading) {
      return;
    }

    const nextPage = this.data.currentPage + 1;
    this.setData({
      loading: true,
      currentPage: nextPage
    });

    // 调用loadWorkOrders加载下一页数据
    this.loadWorkOrders();
  },

  // 根据条件过滤工单
  filterOrders: function(orders) {
    let result = [...orders];

    // 按状态筛选
    if (this.data.currentStatus) {
      result = result.filter(order => order.status === this.data.currentStatus);
    }

    // 按工单类型筛选
    if (this.data.currentType) {
      result = result.filter(order => order.type === this.data.currentType);
    }

    // 按搜索关键词筛选
    if (this.data.searchValue) {
      const keyword = this.data.searchValue.toLowerCase();
      result = result.filter(order =>
        order.id.toLowerCase().includes(keyword) ||
        order.content.title.toLowerCase().includes(keyword) ||
        order.content.description.toLowerCase().includes(keyword) ||
        (order.address && order.address.toLowerCase().includes(keyword))
      );
    }

    return result;
  },

  // 根据条件排序工单
  sortOrders: function(orders) {
    const sortedOrders = [...orders];

    switch (this.data.currentSort) {
      case 'time-desc':
        sortedOrders.sort((a, b) => new Date(b.createTime) - new Date(a.createTime));
        break;
      case 'time-asc':
        sortedOrders.sort((a, b) => new Date(a.createTime) - new Date(b.createTime));
        break;
      case 'priority-desc':
        sortedOrders.sort((a, b) => b.priority - a.priority);
        break;
      case 'priority-asc':
        sortedOrders.sort((a, b) => a.priority - b.priority);
        break;
    }

    return sortedOrders;
  },

  // 获取工单状态文本
  getStatusText(status) {
    try {
      const statusDict = util.getDictByNameEn('work_order_status')[0].children;
      const statusItem = statusDict.find(item => item.nameEn === status);
      return statusItem ? statusItem.nameCn : status;
    } catch (error) {
      console.error('获取状态文本失败', error);
      return status;
    }
  },

  // 获取工单类型文本
  getTypeText(type) {
    try {
      const typeDict = util.getDictByNameEn('work_order_type')[0].children;
      const typeItem = typeDict.find(item => item.nameEn === type);
      return typeItem ? typeItem.nameCn : type;
    } catch (error) {
      console.error('获取类型文本失败', error);
      return type;
    }
  },

  // 搜索工单
  onSearch: function(e) {
    this.setData({
      searchValue: e.detail.value,
      currentPage: 1,
      orders: [],
      filteredOrders: []
    });
    this.loadWorkOrders();
  },

  // 清除搜索
  clearSearch: function() {
    this.setData({
      searchValue: '',
      currentPage: 1,
      orders: [],
      filteredOrders: []
    });
    this.loadWorkOrders();
  },

  // 切换状态筛选
  changeStatus: function(e) {
    const status = e.currentTarget.dataset.status;
    console.log('切换状态筛选:', status);

    this.setData({
      currentStatus: status,
      currentPage: 1,
      showFilterPanel: false,
      orders: [],
      filteredOrders: []
    });

    this.loadWorkOrders();
  },

  // 切换工单类型筛选
  changeType: function(e) {
    const type = e.currentTarget.dataset.type;
    console.log('切换类型筛选:', type);

    this.setData({
      currentType: type,
      currentPage: 1,
      showFilterPanel: false,
      orders: [],
      filteredOrders: []
    });

    this.loadWorkOrders();
  },

  // 切换排序方式
  changeSort: function(e) {
    const sort = e.currentTarget.dataset.sort;
    this.setData({
      currentSort: sort,
      currentPage: 1,
      showFilterPanel: false,
      orders: [],
      filteredOrders: []
    });
    this.loadWorkOrders();
  },

  // 切换筛选面板
  toggleFilterPanel: function() {
    if (this.data.showFilterPanel) {
      // 关闭筛选面板
      const animation = animationHelper.createSlideOutRightAnimation(300);
      this.setData({
        filterPanelAnimation: animation.export()
      });

      // 延迟设置showFilterPanel为false，等待动画完成
      setTimeout(() => {
        this.setData({
          showFilterPanel: false
        });
      }, 300);
    } else {
      // 打开筛选面板
      this.setData({
        showFilterPanel: true
      });

      // 延迟设置动画，确保DOM已经渲染
      setTimeout(() => {
        const animation = animationHelper.createSlideInRightAnimation(300);
        this.setData({
          filterPanelAnimation: animation.export()
        });
      }, 50);
    }
  },

  // 关闭筛选面板
  closeFilterPanel: function() {
    console.log('关闭筛选面板');

    // 先直接关闭面板，不使用动画
    this.setData({
      showFilterPanel: false
    });

    // 显示关闭成功提示
    wx.showToast({
      title: '筛选面板已关闭',
      icon: 'none',
      duration: 1000
    });
  },

  // 阻止滑动穿透
  preventTouchMove: function() {
    return false;
  },

  // 虚拟列表项点击事件
  onItemClick: function(e) {
    const { item, index } = e.detail;

    if (this.data.isSelectMode) {
      // 在选择模式下，点击工单会选中/取消选中
      this.toggleSelectOrderById(item.id);
    } else {
      // 正常模式下，点击工单会导航到详情页
      wx.navigateTo({
        url: `/propertyPackage/pages/property/workorder/detail/index?id=${item.id}`
      });
    }
  },

  // 虚拟列表项长按事件
  onItemLongPress: function(e) {
    const { item, index } = e.detail;

    // 长按进入选择模式并选中当前工单
    if (!this.data.isSelectMode) {
      this.setData({
        isSelectMode: true,
        selectedOrders: [item.id]
      });
    }
  },

  // 导航到工单详情
  navigateToDetail: function(e) {
    if (this.data.isSelectMode) {
      // 在选择模式下，点击工单会选中/取消选中
      this.toggleSelectOrder(e);
    } else {
      // 正常模式下，点击工单会导航到详情页
      const orderId = e.currentTarget.dataset.id;
      console.log('导航到工单详情页，ID:', orderId);
      wx.navigateTo({
        url: `/propertyPackage/pages/property/workorder/detail/index?id=${orderId}`
      });
    }
  },

  // 切换选择模式
  toggleSelectMode: function() {
    if (this.data.isSelectMode) {
      // 关闭选择模式
      this.setData({
        isSelectMode: false,
        selectedOrders: [],
        canProcess: false,
        canComplete: false,
        canCancel: false
      });
    } else {
      // 打开选择模式
      this.setData({
        isSelectMode: true,
        selectedOrders: [],
        canProcess: false,
        canComplete: false,
        canCancel: false
      });
    }
  },

  // 根据ID选中/取消选中工单
  toggleSelectOrderById: function(orderId) {
    const selectedOrders = [...this.data.selectedOrders];

    const index = selectedOrders.indexOf(orderId);
    if (index > -1) {
      // 已选中，取消选中
      selectedOrders.splice(index, 1);
    } else {
      // 未选中，添加到选中列表
      selectedOrders.push(orderId);

      // 添加弹跳动画效果
      const animation = animationHelper.createBounceAnimation(300);
      this.setData({
        statusTabAnimation: animation.export()
      });
    }

    // 更新选中的工单列表
    this.setData({
      selectedOrders: selectedOrders
    });

    // 更新批量操作按钮状态
    this.updateBatchActionStatus();
  },

  // 选中/取消选中工单（兼容旧版本）
  toggleSelectOrder: function(e) {
    const orderId = e.currentTarget.dataset.id;
    this.toggleSelectOrderById(orderId);
  },

  // 全选/取消全选
  toggleSelectAll: function() {
    if (this.data.selectedOrders.length === this.data.filteredOrders.length) {
      // 已全选，取消全选
      this.setData({
        selectedOrders: []
      });
    } else {
      // 未全选，全选
      const allOrderIds = this.data.filteredOrders.map(order => order.id);
      this.setData({
        selectedOrders: allOrderIds
      });
    }

    // 更新批量操作按钮状态
    this.updateBatchActionStatus();
  },

  // 更新批量操作按钮状态
  updateBatchActionStatus: function() {
    if (this.data.selectedOrders.length === 0) {
      // 没有选择任何工单，所有操作按钮禁用
      this.setData({
        canProcess: false,
        canComplete: false,
        canCancel: false
      });
      return;
    }

    // 获取选中工单的详细信息
    const selectedOrderDetails = this.data.filteredOrders.filter(order =>
      this.data.selectedOrders.includes(order.id)
    );

    // 检查是否有可处理的工单（待处理或处理中的工单）
    const hasProcessableOrders = selectedOrderDetails.some(order =>
      order.status === 'pending' || order.status === 'processing'
    );

    // 检查是否有可完成的工单（处理中的工单）
    const hasCompletableOrders = selectedOrderDetails.some(order =>
      order.status === 'processing'
    );

    // 检查是否有可取消的工单（待处理或处理中的工单）
    const hasCancellableOrders = selectedOrderDetails.some(order =>
      order.status === 'pending' || order.status === 'processing'
    );

    // 检查是否选中了已完成或已取消的工单
    const hasCompletedOrders = selectedOrderDetails.some(order =>
      order.status === 'completed'
    );

    const hasCancelledOrders = selectedOrderDetails.some(order =>
      order.status === 'cancelled'
    );

    // 更新按钮状态
    this.setData({
      canProcess: hasProcessableOrders,
      canComplete: hasCompletableOrders && !hasCompletedOrders && !hasCancelledOrders,
      canCancel: hasCancellableOrders && !hasCancelledOrders
    });
  },

  // 执行批量操作
  executeBatchAction: function(e) {
    const action = e.currentTarget.dataset.action;
    const selectedOrders = this.data.selectedOrders;

    if (selectedOrders.length === 0) {
      wx.showToast({
        title: '请先选择工单',
        icon: 'none'
      });
      return;
    }

    switch (action) {
      case 'process':
        // 批量处理暂时使用单个处理页面
        if (selectedOrders.length === 1) {
          wx.navigateTo({
            url: `/pages/property/workorder/process/index?id=${selectedOrders[0]}&type=process`
          });
        } else {
          wx.showToast({
            title: '批量处理功能开发中',
            icon: 'none'
          });
        }
        break;
      case 'assign':
        wx.navigateTo({
          url: `/pages/property/workorder/batch-assign/index?ids=${selectedOrders.join(',')}`
        });
        break;
      case 'complete':
        wx.navigateTo({
          url: `/pages/property/workorder/batch-complete/index?ids=${selectedOrders.join(',')}`
        });
        break;
      case 'cancel':
        this.showCancelConfirm(selectedOrders);
        break;
    }
  },

  // 显示取消确认对话框
  showCancelConfirm: function(orderIds) {
    wx.showModal({
      title: '确认取消',
      content: `确定要取消选中的 ${orderIds.length} 个工单吗？`,
      confirmText: '确定',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          // 执行取消操作
          this.cancelOrders(orderIds);
        }
      }
    });
  },

  // 取消工单
  cancelOrders: function(orderIds) {
    // 这里应该调用API取消工单
    // 暂时使用模拟数据
    wx.showLoading({
      title: '处理中...',
    });

    setTimeout(() => {
      wx.hideLoading();

      wx.showToast({
        title: '工单已取消',
        icon: 'success'
      });

      // 重置选择模式并刷新数据
      this.setData({
        isSelectMode: false,
        selectedOrders: []
      });
      this.loadWorkOrders();
    }, 1000);
  }
});
