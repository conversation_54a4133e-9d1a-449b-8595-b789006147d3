const REQUEST = require('../utils/request.js')

// - **接口路径**: `GET /users-api/v1/community/page`
// - **功能描述**: 通过分页查询小区信息
// - **认证要求**: 需要Authorization Token
// - **响应数据结构**: `ResponseEntityPageInfoCommunity`
//   ```json
// {
//     "errorMessage": null,
//     "code": 0,
//     "data": {
//         "total": 1,
//         "list": [
//             {
//                 "id": "2",
//                 "communityName": "网信科技",
//                 "note": "",
//                 "lng": 0.0,
//                 "lat": 0.0,
//                 "address": "邦宁",
//                 "expandData": "",
//                 "sort": 0,
//                 "createTime": "2025-05-27 10:02:05",
//                 "updateTime": null,
//                 "orgId": "7091961583521234948"
//             }
//         ],
//         "pageNum": 1,
//         "pageSize": 1,
//         "size": 1,
//         "startRow": 0,
//         "endRow": 0,
//         "pages": 1,
//         "prePage": 0,
//         "nextPage": 0,
//         "isFirstPage": true,
//         "isLastPage": true,
//         "hasPreviousPage": false,
//         "hasNextPage": false,
//         "navigatePages": 8,
//         "navigatepageNums": [
//             1
//         ],
//         "navigateFirstPage": 1,
//         "navigateLastPage": 1
//     }
// }
//   ```



//获取小区列表
function communityList(params) {
//   params可能包含小区名称
  return REQUEST.request('/users-api/v1/community/page', 'GET', params, true);
}


//物业获取楼栋列表
function getPropertyBuildingList(params) {
  return REQUEST.request('/manage-api/v1/community/building/page', 'GET', params, true);
}

module.exports = {
    communityList,
    getPropertyBuildingList
}


