// pages/property/workorder/stats/index.js
const workOrderApi = require('@/api/workOrderApi.js');
const util = require('@/utils/util.js');

Page({
  data: {
    darkMode: false,
    isLoading: true,
    activeTab: 'overview', // 当前活动标签页：overview, type, trend
    timeRange: 'week', // 时间范围：week, month, quarter, year
    timeLevel: 1, // 时间级别：1-近7天, 2-近30天, 3-本季度, 4-本年度

    // 统计数据
    statistics: {
      statusCounts: {
        total: 0,
        pending: 0,
        processing: 0,
        completed: 0,
        cancelled: 0,
        accepted: 0,
        complete: 0,
        wait_process: 0,
        cancel: 0
      },
      typeDistribution: {
        repair: 0,
        complaint: 0,
        suggestion: 0,
        other: 0,
        total: 0
      },
      trend: {
        labels: [],
        data: []
      },
      timeEfficiency: {
        avgCompleteTime: '暂无数据',
        avgResponseTime: '暂无数据',
        onTimeCompleteRate: '暂无数据'
      }
    },

    // 字典数据
    workOrderStatusDict: [],
    workOrderTypeDict: [],

    // 计算结果
    completionRate: 0,
    processingRate: 0,
    cancelRate: 0,
    typeDistributionData: []
  },

  onLoad: function () {
    // 检查暗黑模式
    const app = getApp();
    if (app.globalData && app.globalData.darkMode !== undefined) {
      this.setData({
        darkMode: app.globalData.darkMode
      });
    }

    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '工单统计分析'
    });

    // 初始化字典数据
    this.initDictData();

    // 加载统计数据
    this.loadStatistics();
  },

  onPullDownRefresh: function () {
    // 下拉刷新
    this.loadStatistics().then(() => {
      wx.stopPullDownRefresh();
    });
  },

  // 初始化字典数据
  initDictData: function () {
    try {
      const workOrderStatusDict = util.getDictByNameEn('work_order_status')[0].children;
      const workOrderTypeDict = util.getDictByNameEn('work_order_type')[0].children;

      this.setData({
        workOrderStatusDict,
        workOrderTypeDict
      });
    } catch (error) {
      console.error('初始化字典数据失败', error);
    }
  },

  // 加载统计数据
  loadStatistics: function () {
    this.setData({ isLoading: true });

    const params = {
      timeLevel: this.data.timeLevel,
      communityId: wx.getStorageSync('selectedCommunity').id
    };

    // 并行请求三个统计接口
    const promises = [
      workOrderApi.getPropertyStatusCount(params), // 概览统计
      workOrderApi.getPropertyTypeCount(params),   // 类型分布统计
      workOrderApi.getPropertyDataCount(params),    // 趋势分析统计
      workOrderApi.getPropertyValidityCount(params)    // 趋势分析统计
    ];

    return Promise.all(promises)
      .then(([statusData, typeData, trendData, validityData]) => {
        console.log('状态统计:', statusData);
        console.log('类型统计:', typeData);
        console.log('趋势统计:', trendData);
        console.log('处理时效:', validityData);

        // 处理趋势数据的标签
        const trendLabels = this.generateTrendLabels(this.data.timeLevel, trendData.data || []);

        // 提取实际数据
        const statusCounts = statusData || {};
        const typeDistribution = typeData || {};
        const trendDataList = trendData || [];

        // 计算各种比率
        const completionRate = this.calculateCompletionRate(statusCounts);
        const processingRate = this.calculateProcessingRate(statusCounts);
        const cancelRate = this.calculateCancelRate(statusCounts);

        const typeDistributionData = this.getTypeDistributionData(typeDistribution);

        this.setData({
          statistics: {
            statusCounts: statusCounts,
            typeDistribution: typeDistribution,
            trend: {
              labels: trendLabels,
              data: trendDataList
            },
            // 处理时效数据暂时使用默认值，等待后台接口完善
            timeEfficiency: {
              avgCompleteTime: (validityData.avgCompleteTime / 60).toFixed(2) + '分钟',
              avgResponseTime: (validityData.avgResponseTime / 60).toFixed(2) + '分钟',
              onTimeCompleteRate: validityData.onTimeCompleteRate + '%'
            }
          },
          // 计算结果放到data中供WXML使用
          completionRate: completionRate,
          processingRate: processingRate,
          cancelRate: cancelRate,
          typeDistributionData: typeDistributionData,
          isLoading: false
        });
      })
      .catch(error => {
        console.error('加载统计数据失败', error);
        this.setData({ isLoading: false });

        wx.showToast({
          title: '加载失败，请重试',
          icon: 'none'
        });
      });
  },

  // 切换标签页
  switchTab: function (e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({ activeTab: tab });
  },

  // 切换时间范围
  switchTimeRange: function (e) {
    const range = e.currentTarget.dataset.range;

    if (range !== this.data.timeRange) {
      // 映射时间范围到时间级别
      const timeLevelMap = {
        'week': 1,    // 本周
        'month': 2,   // 本月
        'quarter': 3, // 本季度
        'year': 4     // 本年度
      };

      this.setData({
        timeRange: range,
        timeLevel: timeLevelMap[range],
        isLoading: true
      });

      // 重新加载统计数据
      this.loadStatistics();
    }
  },

  // 生成趋势图标签
  generateTrendLabels: function (timeLevel, trendData) {
    const labels = [];

    switch (timeLevel) {
      case 1: // 本周（周一到周日）
        const weekDays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
        // 根据实际数据长度生成标签，通常是7天
        for (let i = 0; i < Math.min(trendData.length, 7); i++) {
          labels.push(weekDays[i]);
        }
        break;
      case 2: // 本月（1号到当前日期或月末）
        const currentDate = new Date().getDate();
        const daysInMonth = new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0).getDate();
        const maxDays = Math.min(trendData.length, daysInMonth);
        for (let i = 1; i <= maxDays; i++) {
          labels.push(i + '日');
        }
        break;
      case 3: // 本季度（按月）
        const currentMonth = new Date().getMonth() + 1;
        const quarterStart = Math.floor((currentMonth - 1) / 3) * 3 + 1;
        for (let i = 0; i < 3; i++) {
          labels.push((quarterStart + i) + '月');
        }
        break;
      case 4: // 本年度（按季度）
        labels.push('第1季度', '第2季度', '第3季度', '第4季度');
        break;
      default:
        // 如果没有匹配的时间级别，使用groupName作为标签
        trendData.forEach(item => {
          labels.push(item.groupName);
        });
    }

    return labels;
  },

  // 导出统计数据
  exportStatistics: function () {
    wx.showToast({
      title: '导出功能开发中',
      icon: 'none'
    });
  },


  // 获取状态显示文本
  getStatusText: function (status) {
    const statusDict = this.data.workOrderStatusDict;
    const statusItem = statusDict.find(item => item.nameEn === status);
    return statusItem ? statusItem.nameCn : status;
  },

  // 获取类型显示文本
  getTypeText: function (type) {
    const typeDict = this.data.workOrderTypeDict;
    const typeItem = typeDict.find(item => item.nameEn === type);
    return typeItem ? typeItem.nameCn : type;
  },

  // 获取类型分布数据
  getTypeDistributionData: function (typeDistribution) {
    const typeData = typeDistribution || this.data.statistics.typeDistribution;
    const result = [];

    // 遍历类型数据，排除total字段
    Object.keys(typeData).forEach(key => {
      if (key !== 'total' && typeData[key] > 0) {
        result.push({
          type: key,
          name: this.getTypeText(key),
          value: typeData[key],
          percentage: typeData.total > 0 ? Math.round((typeData[key] / typeData.total) * 100) : 0
        });
      }
    });

    return result;
  },


  // 计算完成率
  calculateCompletionRate: function (statusCounts) {
      
    const counts = statusCounts;
    const { complete, total } = counts;
    if (!total || total === 0) return 0;
    return Math.round((complete / total) * 100);
  },



  // 计算处理中比率
  calculateProcessingRate: function (statusCounts) {
      
    const { processing, total } = statusCounts;
    if (total === 0) return 0;
    return Math.round((processing / total) * 100);
  },

  // 计算取消比率
  calculateCancelRate: function (statusCounts) {
      
    const { cancel, total } = statusCounts;
    if (total === 0) return 0;
    return Math.round((cancel / total) * 100);
  },

  // 导出统计数据
  exportStatistics: function () {
    wx.showToast({
      title: '导出功能开发中',
      icon: 'none'
    });
  }
});
